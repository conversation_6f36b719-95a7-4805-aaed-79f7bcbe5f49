<?php
/**
 * Custom WordPress configuration for development
 * This file contains additional configurations for the development environment
 */

// Enable WordPress debug mode
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', true);

// Log errors to a specific file
define('WP_DEBUG_LOG', true);
ini_set('log_errors', 1);
ini_set('error_log', '/var/www/html/wp-content/debug.log');

// Increase memory limit
ini_set('memory_limit', '256M');

// Disable file editing from admin
define('DISALLOW_FILE_EDIT', true);

// Enable automatic updates for development
define('WP_AUTO_UPDATE_CORE', true);

// Set custom content directory (if needed)
// define('WP_CONTENT_DIR', '/var/www/html/wp-content');
// define('WP_CONTENT_URL', 'http://localhost:8080/wp-content');

// Custom table prefix (optional, for security)
// $table_prefix = 'wp_dev_';

// Increase post revision limit
define('WP_POST_REVISIONS', 5);

// Set autosave interval (in seconds)
define('AUTOSAVE_INTERVAL', 300);

// Enable WordPress multisite (uncomment if needed)
// define('WP_ALLOW_MULTISITE', true);

// Custom upload directory
// define('UPLOADS', 'wp-content/uploads');

// Force SSL (uncomment for HTTPS development)
// define('FORCE_SSL_ADMIN', true);

// Custom WordPress salts (these should be unique for each installation)
// You can generate these at: https://api.wordpress.org/secret-key/1.1/salt/
define('AUTH_KEY',         'put your unique phrase here');
define('SECURE_AUTH_KEY',  'put your unique phrase here');
define('LOGGED_IN_KEY',    'put your unique phrase here');
define('NONCE_KEY',        'put your unique phrase here');
define('AUTH_SALT',        'put your unique phrase here');
define('SECURE_AUTH_SALT', 'put your unique phrase here');
define('LOGGED_IN_SALT',   'put your unique phrase here');
define('NONCE_SALT',       'put your unique phrase here');
