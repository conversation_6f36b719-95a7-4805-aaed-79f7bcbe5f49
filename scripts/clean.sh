#!/bin/bash

# WordPress Docker Development Environment Clean Script

echo "🧹 Cleaning WordPress Development Environment..."

# Stop containers
docker-compose down

# Remove volumes (this will delete all database data)
echo "⚠️  This will remove all database data. Are you sure? (y/N)"
read -r response
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    echo "🗑️  Removing Docker volumes..."
    docker-compose down -v
    docker volume prune -f
    
    echo "✅ Environment cleaned successfully!"
    echo "💡 Run ./scripts/start.sh to start fresh"
else
    echo "❌ Clean operation cancelled"
fi
