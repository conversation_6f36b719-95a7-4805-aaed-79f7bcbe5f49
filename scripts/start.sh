#!/bin/bash

# WordPress Docker Development Environment Startup Script

echo "🚀 Starting WordPress Development Environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
fi

# Create wp-content directory if it doesn't exist
if [ ! -d "wp-content" ]; then
    echo "📁 Creating wp-content directory..."
    mkdir -p wp-content/{themes,plugins,uploads}
fi

# Start Docker containers
echo "🐳 Starting Docker containers..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check if WordPress is accessible
echo "🔍 Checking WordPress accessibility..."
if curl -f http://localhost:8080 > /dev/null 2>&1; then
    echo "✅ WordPress is running at http://localhost:8080"
    echo "✅ phpMyAdmin is running at http://localhost:8081"
    echo ""
    echo "🎉 Development environment is ready!"
    echo ""
    echo "📋 Quick Access:"
    echo "   WordPress: http://localhost:8080"
    echo "   phpMyAdmin: http://localhost:8081"
    echo "   Database: localhost:3306"
    echo ""
    echo "🔑 Database Credentials:"
    echo "   Database: wordpress_db"
    echo "   Username: wordpress"
    echo "   Password: wordpress_password"
else
    echo "⚠️  WordPress might still be starting up. Please wait a moment and check http://localhost:8080"
fi
