#!/bin/bash

# WordPress CLI Helper Script

if [ $# -eq 0 ]; then
    echo "Usage: ./scripts/wp-cli.sh [wp-cli command]"
    echo ""
    echo "Examples:"
    echo "  ./scripts/wp-cli.sh core version"
    echo "  ./scripts/wp-cli.sh plugin list"
    echo "  ./scripts/wp-cli.sh theme list"
    echo "  ./scripts/wp-cli.sh user list"
    echo "  ./scripts/wp-cli.sh db export"
    exit 1
fi

# Run WP-CLI command in the WordPress container
docker-compose run --rm wpcli wp "$@"
