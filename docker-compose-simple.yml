services:
  # WordPress service
  wordpress:
    image: wordpress:latest
    container_name: tailpress_wordpress
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      WORDPRESS_DB_HOST: db:3306
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress_password
      WORDPRESS_DB_NAME: wordpress_db
      WORDPRESS_DEBUG: 1
    volumes:
      - ./wp-content:/var/www/html/wp-content
    depends_on:
      - db
    networks:
      - wordpress_network

  # MySQL database service
  db:
    image: mysql:8.0
    container_name: tailpress_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: wordpress_db
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress_password
      MYSQL_ROOT_PASSWORD: root_password
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - wordpress_network

volumes:
  db_data:

networks:
  wordpress_network:
    driver: bridge
