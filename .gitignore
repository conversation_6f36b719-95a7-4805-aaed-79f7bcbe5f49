# WordPress Core Files (we'll use Docker image)
/wordpress/

# Environment files
.env

# WordPress uploads and cache
wp-content/uploads/
wp-content/cache/
wp-content/backup*/
wp-content/backups/
wp-content/upgrade/

# WordPress debug logs
wp-content/debug.log
*.log

# Plugin and theme development
wp-content/plugins/*/node_modules/
wp-content/themes/*/node_modules/
wp-content/plugins/*/.sass-cache/
wp-content/themes/*/.sass-cache/

# Compiled assets
*.css.map
*.js.map

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/settings.json
.idea/
*.swp
*.swo
*~

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Composer
vendor/
composer.lock

# Docker
.docker/

# Temporary files
*.tmp
*.temp
