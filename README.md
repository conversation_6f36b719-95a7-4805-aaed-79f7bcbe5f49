# TailPress - WordPress Development Environment

A Docker-based WordPress development environment optimized for modern development workflows.

## 🚀 Quick Start

### Prerequisites
- [Docker Desktop](https://www.docker.com/products/docker-desktop) installed and running
- [VS Code](https://code.visualstudio.com/) (recommended)

### Setup
1. Clone or download this repository
2. Open the project in VS Code
3. Run the startup script:
   ```bash
   ./scripts/start.sh
   ```

### Access Your Site
- **WordPress**: http://localhost:8080
- **phpMyAdmin**: http://localhost:8081
- **Database**: localhost:3306

## 📋 Default Credentials

### Database
- **Host**: localhost:3306
- **Database**: wordpress_db
- **Username**: wordpress
- **Password**: wordpress_password
- **Root Password**: root_password

### WordPress Admin
After running the setup, visit http://localhost:8080 to complete the WordPress installation.

## 🛠️ Available Scripts

### Start Environment
```bash
./scripts/start.sh
```
Starts all Docker containers and sets up the development environment.

### Stop Environment
```bash
./scripts/stop.sh
```
Stops all running containers while preserving data.

### Clean Environment
```bash
./scripts/clean.sh
```
⚠️ **Warning**: This removes all data including the database. Use with caution.

### WP-CLI Commands
```bash
./scripts/wp-cli.sh [command]
```

Examples:
```bash
./scripts/wp-cli.sh core version
./scripts/wp-cli.sh plugin list
./scripts/wp-cli.sh theme install twentytwentyfour
./scripts/wp-cli.sh user <NAME_EMAIL> --role=administrator
```

## 📁 Project Structure

```
tailpress/
├── docker-compose.yml      # Docker services configuration
├── uploads.ini            # PHP upload settings
├── wp-config-custom.php   # Custom WordPress configuration
├── .env.example          # Environment variables template
├── scripts/              # Utility scripts
│   ├── start.sh         # Start development environment
│   ├── stop.sh          # Stop environment
│   ├── clean.sh         # Clean environment
│   └── wp-cli.sh        # WP-CLI helper
├── wp-content/           # WordPress content (auto-created)
│   ├── themes/          # Custom themes
│   ├── plugins/         # Custom plugins
│   └── uploads/         # Media uploads
└── README.md            # This file
```

## 🔧 Development Features

### Debug Mode
- WordPress debug mode is enabled by default
- Debug logs are saved to `wp-content/debug.log`
- Error display is disabled in browser (check logs instead)

### File Uploads
- Maximum upload size: 64MB
- Memory limit: 256MB
- Execution time: 300 seconds

### Database Management
- phpMyAdmin available at http://localhost:8081
- Direct MySQL access on port 3306
- Persistent data storage

## 🎨 Theme Development

Place your custom themes in the `wp-content/themes/` directory. They will be automatically available in WordPress.

## 🔌 Plugin Development

Place your custom plugins in the `wp-content/plugins/` directory. They will be automatically available in WordPress.

## 🐳 Docker Services

### WordPress
- **Image**: wordpress:latest
- **Port**: 8080
- **Container**: tailpress_wordpress

### MySQL
- **Image**: mysql:8.0
- **Port**: 3306
- **Container**: tailpress_mysql

### phpMyAdmin
- **Image**: phpmyadmin/phpmyadmin:latest
- **Port**: 8081
- **Container**: tailpress_phpmyadmin

### WP-CLI
- **Image**: wordpress:cli
- **Container**: tailpress_wpcli

## 🔒 Security Notes

- This setup is for **development only**
- Default passwords should be changed for production
- Debug mode should be disabled in production
- File editing is disabled in WordPress admin for security

## 🆘 Troubleshooting

### WordPress not loading
1. Check if Docker is running: `docker ps`
2. Wait a few moments for services to start
3. Check logs: `docker-compose logs wordpress`

### Database connection issues
1. Ensure MySQL container is running: `docker-compose ps`
2. Check database logs: `docker-compose logs db`
3. Verify credentials in docker-compose.yml

### Port conflicts
If ports 8080 or 8081 are in use, modify the ports in `docker-compose.yml`:
```yaml
ports:
  - "8090:80"  # Change 8080 to 8090
```

## 📚 Additional Resources

- [WordPress Developer Documentation](https://developer.wordpress.org/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [WP-CLI Documentation](https://wp-cli.org/)

## 🤝 Contributing

Feel free to submit issues and enhancement requests!
